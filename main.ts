import { Plugin } from "obsidian";
import { FileCreateModal } from "./componets/modal";

export default class MyPlugin extends Plugin {
	async onload() {
		console.log("loading plugin: " + this.manifest.name);
		this.addCommand({
			id: "create-layout-note",
			name: "Create new note with layout",
			callback: () => {
				new FileCreateModal(this.app).open();
			},
		})
	}

	async unload() {
		console.log("unloading plugin: " + this.manifest.name);

	}
}
