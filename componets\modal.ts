import { App, Modal } from "obsidian";

// Modal for file creation options
//class is a blueprint for creating objects
export class FileCreateModal extends Modal {
	// constructor creates a new instance of the modal
	//instance means a new version of the modal

	constructor(app: App) {
		super(app);

		// Create container for centered buttons
		const container = this.contentEl.createDiv();
		container.style.display = "flex";
		container.style.flexDirection = "column";
		container.style.alignItems = "center";
		container.style.gap = "10px";
		container.style.padding = "20px";

		// Create title
		const title = container.createEl("h3");
		title.textContent = "Fortellr";
		title.style.marginBottom = "20px";

		// Create button container for horizontal centering
		const buttonContainer = container.createDiv();
		buttonContainer.style.display = "flex";
		buttonContainer.style.gap = "15px";
		buttonContainer.style.justifyContent = "center";

		// Create "black" button
		const blackButton = buttonContainer.createEl("button");
		blackButton.textContent = "black";
		blackButton.style.padding = "10px 20px";
		blackButton.style.fontSize = "14px";
		blackButton.style.cursor = "pointer";

		// Create "template 1" button
		const template1Button = buttonContainer.createEl("button");
		template1Button.textContent = "template 1";
		template1Button.style.padding = "10px 20px";
		template1Button.style.fontSize = "14px";
		template1Button.style.cursor = "pointer";

		// Add click handlers (you can customize these)
		blackButton.addEventListener("click", () => {
			console.log("Black button clicked");
			// Add your logic here
		});

		template1Button.addEventListener("click", () => {
			console.log("Template 1 button clicked");
			// Add your logic here
		});
	}
}
